{"roots": ["server"], "packages": [{"name": "server", "version": "2.0.0", "dependencies": ["bcrypt", "crypto", "dart_jsonwebtoken", "equatable", "http", "json_annotation", "logging", "meta", "mongo_dart", "shared", "shelf", "shelf_cors_headers", "shelf_router", "shelf_static", "shelf_web_socket", "uuid", "web_socket_channel"], "devDependencies": ["build_runner", "json_serializable", "lints", "<PERSON><PERSON>", "test"]}, {"name": "json_serializable", "version": "6.9.5", "dependencies": ["analyzer", "async", "build", "build_config", "collection", "dart_style", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "build_runner", "version": "2.4.14", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web_socket_channel", "yaml"]}, {"name": "<PERSON><PERSON>", "version": "5.4.6", "dependencies": ["analyzer", "build", "code_builder", "collection", "dart_style", "matcher", "meta", "path", "source_gen", "test_api"]}, {"name": "test", "version": "1.26.2", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "meta", "version": "1.17.0", "dependencies": []}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "dart_jsonwebtoken", "version": "2.17.0", "dependencies": ["clock", "collection", "convert", "crypto", "ed25519_edwards", "pointycastle"]}, {"name": "bcrypt", "version": "1.1.3", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "mongo_dart", "version": "0.10.5", "dependencies": ["basic_utils", "bson", "collection", "crypto", "decimal", "fixnum", "logging", "mongo_dart_query", "path", "pool", "sasl_scram", "uuid", "vy_string_utils"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "web_socket_channel", "version": "2.4.5", "dependencies": ["async", "crypto", "stream_channel", "web"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "shelf_cors_headers", "version": "0.1.5", "dependencies": ["shelf"]}, {"name": "shelf_web_socket", "version": "2.0.1", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf_router", "version": "1.1.4", "dependencies": ["http_methods", "meta", "shelf"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "shared", "version": "2.0.0", "dependencies": ["crypto", "equatable", "http", "json_annotation", "meta", "uuid", "web_socket_channel"]}, {"name": "source_helper", "version": "1.3.5", "dependencies": ["analyzer", "collection", "source_gen"]}, {"name": "source_gen", "version": "2.0.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "dart_style", "version": "3.1.0", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "build", "version": "2.4.2", "dependencies": ["analyzer", "async", "convert", "crypto", "glob", "logging", "meta", "package_config", "path"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "analyzer", "version": "7.5.2", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "watcher", "version": "1.1.2", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "8.0.0", "dependencies": ["async", "build", "build_config", "build_resolvers", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.4.4", "dependencies": ["analyzer", "async", "build", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform", "yaml"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "test_api", "version": "0.7.6", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "test_core", "version": "0.6.11", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "coverage", "version": "1.14.1", "dependencies": ["args", "cli_config", "glob", "logging", "meta", "package_config", "path", "source_maps", "stack_trace", "vm_service", "yaml"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "ed25519_edwards", "version": "0.3.1", "dependencies": ["adaptive_number", "collection", "convert", "crypto"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "pointycastle", "version": "3.9.1", "dependencies": ["collection", "convert", "js"]}, {"name": "vy_string_utils", "version": "0.4.6", "dependencies": ["power_extensions"]}, {"name": "sasl_scram", "version": "0.1.1", "dependencies": ["buffer", "collection", "crypto", "saslprep"]}, {"name": "decimal", "version": "3.2.4", "dependencies": ["intl", "rational"]}, {"name": "basic_utils", "version": "5.7.0", "dependencies": ["http", "json_annotation", "logging", "pointycastle"]}, {"name": "mongo_dart_query", "version": "5.0.2", "dependencies": ["bson", "fixnum", "meta"]}, {"name": "bson", "version": "5.0.7", "dependencies": ["decimal", "fixnum", "packages_extensions", "power_extensions", "rational", "uuid"]}, {"name": "web", "version": "0.5.1", "dependencies": []}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "http_methods", "version": "1.1.1", "dependencies": []}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "_fe_analyzer_shared", "version": "85.0.0", "dependencies": ["meta"]}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "built_value", "version": "8.10.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "vm_service", "version": "15.0.2", "dependencies": []}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "cli_config", "version": "0.2.0", "dependencies": ["args", "yaml"]}, {"name": "adaptive_number", "version": "1.0.0", "dependencies": ["fixnum"]}, {"name": "power_extensions", "version": "0.2.3", "dependencies": []}, {"name": "saslprep", "version": "1.0.3", "dependencies": ["unorm_dart"]}, {"name": "buffer", "version": "1.2.3", "dependencies": []}, {"name": "rational", "version": "2.2.3", "dependencies": []}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "packages_extensions", "version": "0.1.1", "dependencies": ["decimal", "power_extensions", "rational"]}, {"name": "unorm_dart", "version": "0.3.1+1", "dependencies": []}], "configVersion": 1}