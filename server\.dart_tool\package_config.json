{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-85.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "adaptive_number", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/adaptive_number-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "analyzer", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/analyzer-7.5.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "args", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "basic_utils", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/basic_utils-5.7.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "bcrypt", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/bcrypt-1.1.3", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "boolean_selector", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "bson", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/bson-5.0.7", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "buffer", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/buffer-1.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "build", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/build-2.4.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_config", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/build_config-1.1.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_daemon", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/build_daemon-4.0.4", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_resolvers", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_runner", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/build_runner-2.4.14", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_runner_core", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "built_collection", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/built_value-8.10.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "checked_yaml", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "cli_config", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/cli_config-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "clock", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "code_builder", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/code_builder-4.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "collection", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "convert", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "coverage", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/coverage-1.14.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "crypto", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "dart_jsonwebtoken", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/dart_jsonwebtoken-2.17.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dart_style", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/dart_style-3.1.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "decimal", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/decimal-3.2.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ed25519_edwards", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/ed25519_edwards-0.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "equatable", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/equatable-2.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "file", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fixnum", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "frontend_server_client", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "graphs", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/graphs-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_methods", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/http_methods-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "http_multi_server", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "intl", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/intl-0.20.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "io", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/io-1.0.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "js", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/js-0.7.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "json_annotation", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "json_serializable", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/json_serializable-6.9.5", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "lints", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "logging", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "matcher", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "meta", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/meta-1.17.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "mime", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "<PERSON><PERSON>", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/mockito-5.4.6", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "mongo_dart", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/mongo_dart-0.10.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "mongo_dart_query", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/mongo_dart_query-5.0.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "node_preamble", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/node_preamble-2.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "package_config", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "packages_extensions", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/packages_extensions-0.1.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "path", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pointycastle", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/pointycastle-3.9.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pool", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "power_extensions", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/power_extensions-0.2.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pub_semver", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pubspec_parse", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "rational", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/rational-2.2.3", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "sasl_scram", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/sasl_scram-0.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "saslprep", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/saslprep-1.0.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "shared", "rootUri": "../../shared", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "shelf", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/shelf-1.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shelf_cors_headers", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/shelf_cors_headers-0.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "shelf_packages_handler", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_router", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/shelf_router-1.1.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_static", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/shelf_static-1.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf_web_socket", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_gen", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/source_gen-2.0.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "source_helper", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/source_helper-1.3.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "source_map_stack_trace", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_maps", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/source_maps-0.10.13", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_span", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stack_trace", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/test-1.26.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "test_api", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/test_api-0.7.6", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "test_core", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/test_core-0.6.11", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "timing", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/timing-1.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "typed_data", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "unorm_dart", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/unorm_dart-0.3.1+1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "uuid", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vm_service", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/vm_service-15.0.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "vy_string_utils", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/vy_string_utils-0.4.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "watcher", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/watcher-1.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "web", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/web-0.5.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web_socket_channel", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "webkit_inspection_protocol", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "yaml", "rootUri": "file:///app/.pub-cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "server", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.8"}], "generator": "pub", "generatorVersion": "3.8.1", "pubCache": "file:///app/.pub-cache"}