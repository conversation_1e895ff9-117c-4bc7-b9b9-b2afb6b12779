import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shared/shared.dart';

// Core imports
import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';
import 'core/constants/app_constants.dart';
import 'components/responsive_builder.dart';

// Providers/Cubits
import 'providers/theme_cubit.dart';
import 'providers/auth_cubit.dart';
import 'providers/notification_cubit.dart';
import 'providers/websocket_cubit.dart';

// Services
import 'services/api_service.dart';
import 'services/websocket_service.dart';
import 'services/storage_service.dart';

/// Main entry point for the Quester Flutter application
///
/// Initializes the app with comprehensive setup including:
/// - Local storage initialization
/// - System UI configuration
/// - Orientation settings
/// - Universal responsive framework
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize local storage
  await _initializeStorage();

  // Configure system UI for universal platform support
  await _configureSystemUI();

  // Set preferred orientations for responsive design
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  runApp(const QuesterApp());
}

/// Initialize local storage systems
Future<void> _initializeStorage() async {
  try {
    // Initialize SharedPreferences for app settings
    await SharedPreferences.getInstance();

    // Initialize Hive for complex data storage (if available)
    // await Hive.initFlutter();
  } catch (e) {
    debugPrint('Storage initialization warning: $e');
  }
}

/// Configure system UI for universal platform support
Future<void> _configureSystemUI() async {
  // Set system UI overlay style with adaptive theming
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );
}

/// Universal Quester Application
///
/// Provides comprehensive BLoC providers and Material 3 theming
/// for the entire application with universal responsive design
class QuesterApp extends StatelessWidget {
  const QuesterApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: _buildBlocProviders(),
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          return MaterialApp.router(
            title: ClientConstants.appName,
            debugShowCheckedModeBanner: false,

            // Universal Material 3 theming
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeState.themeMode,

            // Router configuration
            routerConfig: AppRouter.router,

            // Universal responsive framework
            builder: (context, child) => _buildResponsiveWrapper(context, child),

            // Localization support
            supportedLocales: const [
              Locale('en', 'US'),
              Locale('es', 'ES'),
              Locale('fr', 'FR'),
            ],
          );
        },
      ),
    );
  }

  /// Build BLoC providers with optimized dependency injection
  List<BlocProvider> _buildBlocProviders() {
    // Create singleton services for better performance
    final apiService = ApiService();
    final webSocketService = WebSocketService();
    final storageService = StorageService();

    return [
      // Theme management with persistent storage
      BlocProvider<ThemeCubit>(
        create: (_) => ThemeCubit(storageService: storageService),
        lazy: false, // Load immediately for theme initialization
      ),

      // Authentication management with shared services
      BlocProvider<AuthCubit>(
        create: (_) => AuthCubit(
          apiService: apiService,
          webSocketService: webSocketService,
        ),
        lazy: false, // Load immediately for auth state
      ),

      // Notification management
      BlocProvider<NotificationCubit>(
        create: (_) => NotificationCubit(),
        lazy: true, // Load on demand
      ),

      // WebSocket management with shared service
      BlocProvider<WebSocketCubit>(
        create: (_) => WebSocketCubit(webSocketService: webSocketService),
        lazy: true, // Load on demand
      ),
    ];
  }

  /// Build universal responsive wrapper with Material 3 breakpoints
  Widget _buildResponsiveWrapper(BuildContext context, Widget? child) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            // Responsive text scaling
            textScaler: TextScaler.linear(_getResponsiveTextScale(deviceType)),
          ),
          child: child!,
        );
      },
    );
  }

  /// Get responsive text scale based on device type
  double _getResponsiveTextScale(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 1.0;
      case DeviceType.tablet:
        return 1.1;
      case DeviceType.desktop:
        return 1.2;
      case DeviceType.wideDesktop:
        return 1.3;
    }
  }
}
